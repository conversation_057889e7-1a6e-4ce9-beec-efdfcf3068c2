#!/bin/bash -e

# Determine the script's directory
SCRIPTPATH="$( cd "$(dirname "$0")" ; pwd -P )"

# Define variables
IMAGE_NAME="debezium"
DOCKERFILE_PATH="${SCRIPTPATH}/../docker/debezium.dockerfile"
BUILD_CONTEXT="${SCRIPTPATH}/../docker"
TAG="latest"

# Build the Docker image
#docker build -t ${IMAGE_NAME}:${TAG} -f ${DOCKERFILE_PATH} ${BUILD_CONTEXT}
#nerdctl build -t shqear/debezium:latest -f ${DOCKERFILE_PATH} ${BUILD_CONTEXT}


export BUILDKIT_HOST=tcp://127.0.0.1:1234

buildctl build \
  --frontend dockerfile.v0 \
  --progress=plain \
  --local context="${BUILD_CONTEXT}" \
  --local dockerfile="$(dirname "${DOCKERFILE_PATH}")" \
  --opt filename="$(basename "${DOCKERFILE_PATH}")" \
  --output type=image,name=shqear/debezium:latest,push=true


# Print success message
#echo "Docker image ${IMAGE_NAME}:${TAG} built successfully."
echo "Docker image shqear/debezium:latest built successfully."