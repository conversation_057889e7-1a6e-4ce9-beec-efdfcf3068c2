# ---------------------
# 1) Declare ARGs for base image version
# ---------------------
ARG STRIMZI_VERSION=0.42.0-kafka-3.7.0

# Use the St<PERSON>zi Kafka image as the base
FROM quay.io/strimzi/kafka:${STRIMZI_VERSION}

# ---------------------
# 2) Re-declare additional ARGs after FROM
# ---------------------
# Define Debezium version and connectors to install.
# You can update DEBEZIUM_CONNECTORS to include additional connectors if available (e.g., "oracle db2").
ARG DEBEZIUM_VERSION=3.0.7.Final
ARG DEBEZIUM_CONNECTORS="mysql mariadb vitess postgres mongodb sqlserver jdbc"

# Define Confluent Platform version for Avro converter compatibility
ARG CONFLUENT_VERSION=7.5.3

# Make these available as environment variables for later RUN steps.
ENV DEBEZIUM_VERSION=${DEBEZIUM_VERSION}
ENV DEBEZIUM_CONNECTORS=${DEBEZIUM_CONNECTORS}
ENV CONFLUENT_VERSION=${CONFLUENT_VERSION}

# Metadata
LABEL maintainer="Khaled AbuShqear <<EMAIL>>"

# ---------------------
# 3) Switch to root and prepare the image
# ---------------------
USER root:root

# Ensure that tar and gzip are installed (if not already available)
RUN microdnf install -y tar gzip && microdnf clean all

# Create the directories for plugins
RUN mkdir -p /opt/kafka/plugins/debezium /opt/kafka/plugins/confluent

# ---------------------
# 4) Download & extract each Debezium connector
# ---------------------
RUN for connector in $DEBEZIUM_CONNECTORS; do \
      echo "Installing Debezium connector: $connector (version $DEBEZIUM_VERSION)"; \
      if curl -sSL --fail "https://repo1.maven.org/maven2/io/debezium/debezium-connector-$connector/$DEBEZIUM_VERSION/debezium-connector-$connector-$DEBEZIUM_VERSION-plugin.tar.gz" \
      | tar zxvf - -C /opt/kafka/plugins/debezium; then \
        echo "✓ Successfully installed Debezium connector: $connector"; \
      else \
        echo "✗ Failed to install Debezium connector: $connector"; \
        exit 1; \
      fi; \
    done; \
    # Validate Debezium installation \
    echo "=== Debezium connectors validation ==="; \
    ls -la /opt/kafka/plugins/debezium/; \
    echo "=== Debezium installation completed ==="

# ---------------------
# 5) Optionally download the Debezium Interceptor
# ---------------------
RUN curl -sSL "https://repo1.maven.org/maven2/io/debezium/debezium-interceptor/$DEBEZIUM_VERSION/debezium-interceptor-$DEBEZIUM_VERSION.jar" \
    -o /opt/kafka/plugins/debezium/debezium-interceptor-$DEBEZIUM_VERSION.jar \
  || echo "Skipping Debezium interceptor download..."

# ---------------------
# 6) Download Confluent Avro Converter and dependencies
# ---------------------
RUN echo "Installing Confluent Avro Converter (version $CONFLUENT_VERSION)"; \
    # Function to download and validate JAR files \
    download_and_validate() { \
        local url="$1"; \
        local output="$2"; \
        local component="$3"; \
        echo "Downloading $component..."; \
        if curl -sSL --fail "$url" -o "$output"; then \
            # Validate the file is a proper JAR (should start with PK magic bytes) \
            if [ "$(head -c 2 "$output" | od -An -tx1 | tr -d ' ')" = "504b" ]; then \
                # Check minimum file size (should be larger than 1KB for real JARs) \
                local size=$(stat -c%s "$output" 2>/dev/null || stat -f%z "$output" 2>/dev/null || echo "0"); \
                if [ "$size" -gt 1024 ]; then \
                    echo "✓ Successfully downloaded and validated $component ($size bytes)"; \
                else \
                    echo "✗ Error: $component file too small ($size bytes), likely invalid"; \
                    cat "$output" | head -5; \
                    exit 1; \
                fi; \
            else \
                echo "✗ Error: $component is not a valid JAR file (wrong magic bytes)"; \
                cat "$output" | head -5; \
                exit 1; \
            fi; \
        else \
            echo "✗ Error: Failed to download $component from $url"; \
            exit 1; \
        fi; \
    }; \
    # Download and validate each component \
    download_and_validate "https://packages.confluent.io/maven/io/confluent/kafka-connect-avro-converter/$CONFLUENT_VERSION/kafka-connect-avro-converter-$CONFLUENT_VERSION.jar" \
        "/opt/kafka/plugins/confluent/kafka-connect-avro-converter-$CONFLUENT_VERSION.jar" \
        "kafka-connect-avro-converter"; \
    download_and_validate "https://packages.confluent.io/maven/io/confluent/kafka-avro-serializer/$CONFLUENT_VERSION/kafka-avro-serializer-$CONFLUENT_VERSION.jar" \
        "/opt/kafka/plugins/confluent/kafka-avro-serializer-$CONFLUENT_VERSION.jar" \
        "kafka-avro-serializer"; \
    download_and_validate "https://packages.confluent.io/maven/io/confluent/kafka-schema-serializer/$CONFLUENT_VERSION/kafka-schema-serializer-$CONFLUENT_VERSION.jar" \
        "/opt/kafka/plugins/confluent/kafka-schema-serializer-$CONFLUENT_VERSION.jar" \
        "kafka-schema-serializer"; \
    download_and_validate "https://packages.confluent.io/maven/io/confluent/kafka-schema-registry-client/$CONFLUENT_VERSION/kafka-schema-registry-client-$CONFLUENT_VERSION.jar" \
        "/opt/kafka/plugins/confluent/kafka-schema-registry-client-$CONFLUENT_VERSION.jar" \
        "kafka-schema-registry-client"; \
    download_and_validate "https://packages.confluent.io/maven/io/confluent/common-config/$CONFLUENT_VERSION/common-config-$CONFLUENT_VERSION.jar" \
        "/opt/kafka/plugins/confluent/common-config-$CONFLUENT_VERSION.jar" \
        "common-config"; \
    download_and_validate "https://packages.confluent.io/maven/io/confluent/common-utils/$CONFLUENT_VERSION/common-utils-$CONFLUENT_VERSION.jar" \
        "/opt/kafka/plugins/confluent/common-utils-$CONFLUENT_VERSION.jar" \
        "common-utils"; \
    # Final validation - list all downloaded JARs with sizes \
    echo "=== Final validation of Confluent JARs ==="; \
    ls -la /opt/kafka/plugins/confluent/; \
    echo "=== Confluent Avro Converter installation completed successfully ==="

# ---------------------
# 7) Return to the default Strimzi non-root user
# ---------------------
USER 1001
