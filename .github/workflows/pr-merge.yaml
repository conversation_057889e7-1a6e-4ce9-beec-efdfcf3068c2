---
name: Publish Kafka Registry Image
run-name: "Build & Publish - tag: ${{  github.ref_name == 'master' && 'latest' || github.ref_name }}"

on:
  push:
    branches: [ "master" ]
    tags:
      - 'v*.*.*'
  release:
    types: [ published ]
  create:
    branches:
      - '**'

  workflow_dispatch:

jobs:

  docker-build:
    uses: careem/shared-workflows/.github/workflows/ecr-publish.yml@master
    secrets: inherit
    with:
      tag: ${{  github.ref_name == 'master' && 'latest' || github.ref_name }}
      ecr-name: "connect-flow-debezium"
      docker-file: docker/debezium.dockerfile
      docker-build-folder: "./docker"
