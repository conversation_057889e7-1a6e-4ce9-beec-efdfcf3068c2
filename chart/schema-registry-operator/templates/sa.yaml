apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ .Release.Name }}-schema-registry-operator
  namespace: {{ .Release.Namespace }}
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: {{ .Release.Name }}-schema-registry-operator
  # "namespace" omitted if was ClusterRoles because are not namespaced
  # namespace: {{ .Release.Namespace }}
  # labels:
  #   k8s-app: <YOUR_APP_LABEL>
rules:
# Framework: knowing which other operators are running (i.e. peering).
- apiGroups: [kopf.dev]
  resources: [clusterkopfpeerings]
  verbs: [list, watch, patch, get]

# Framework: runtime observation of namespaces & CRDs (addition/deletion).
- apiGroups: [apiextensions.k8s.io]
  resources: [customresourcedefinitions]
  verbs: [list, watch]
- apiGroups: [""]
  resources: [namespaces]
  verbs: [list, watch]

# Framework: admission webhook configuration management.
- apiGroups: [admissionregistration.k8s.io/v1, admissionregistration.k8s.io/v1beta1]
  resources: [validatingwebhookconfigurations, mutatingwebhookconfigurations]
  verbs: [create, patch]

# Application: read-only access for watching cluster-wide.
- apiGroups: [kopf.dev]
  resources: [kopfexamples]
  verbs: [list, watch]

- apiGroups:
  - kafka.careem.com
  resources:
  - schemaregistryschemas
  - schemaregistryschemas/status
  - schemas
  - schemas/status
  verbs:
  - get
  - list
  - watch
  - create
  - patch
  - update
  - delete
# Framework: posting the events about the handlers progress/errors.
- apiGroups: [""]
  resources: [events]
  verbs: [create]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: {{ .Release.Name }}-schema-registry-operator
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: {{ .Release.Name }}-schema-registry-operator
subjects:
  - kind: ServiceAccount
    name: {{ .Release.Name }}-schema-registry-operator
    namespace: {{ .Release.Namespace }}
