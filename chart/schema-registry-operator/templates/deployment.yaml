apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ .Release.Name }}-kafka-connect-operator
spec:
  replicas: {{ .Values.replicaCount }}
  strategy:
    type: Recreate
  selector:
    matchLabels:
      app: {{ .Release.Name }}-schema-registry-operator
  template:
    metadata:
      annotations:
      {{- with .Values.podAnnotations }}
          {{- toYaml . | nindent 9 }}
      {{- end }}
      labels:
        app: {{ .Release.Name }}-schema-registry-operator
    spec:
      {{- with .Values.image.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ .Release.Name }}-schema-registry-operator
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
      - image: {{ .Values.image.imageName }}
        name: schema-registry-operator
        env: {{ .Values.env | toYaml | nindent 9 }}
        securityContext:
          {{- toYaml .Values.securityContext | nindent 12 }}
        imagePullPolicy: {{ .Values.image.pullPolicy }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
