apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  name: kafkaconnecttransforms.kafka.careem.com
spec:
  group: kafka.careem.com
  names:
    plural: kafkaconnecttransforms
    singular: kafkaconnecttransform
    kind: KafkaConnectTransform
    shortNames:
      - kct
  scope: Namespaced
  versions:
    - name: v1beta1
      served: true
      storage: true
      schema:
        openAPIV3Schema:
          type: object
          properties:
            spec:
              type: object
              properties:
                class:
                  type: string
                  description: "Kafka Single Message Transform (SMT) class."
                config:
                  type: object
                  additionalProperties: true
                  description: "Configuration parameters for the transformation."
            status:
              type: object
{{/*              x-kubernetes-preserve-unknown-fields: true*/}}
              properties:
                lastUsed:
                  type: string
                  format: date-time
                  description: "Last time this transformation was applied."
      additionalPrinterColumns:
        - name: Class
          type: string
          jsonPath: ".spec.class"
          description: "Kafka SMT class."
        - name: Last Used
          type: string
          format: date-time
          jsonPath: ".status.lastUsed"
          description: "Timestamp of last use."
