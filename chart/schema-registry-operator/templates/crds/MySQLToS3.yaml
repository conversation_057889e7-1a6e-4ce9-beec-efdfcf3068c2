apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  name: mysqltoss3.kafka.careem.com
spec:
  group: kafka.careem.com
  names:
    plural: mysqltoss3
    singular: mysqltoss3
    kind: MySQLToS3
    shortNames:
      - mysql2s3
  scope: Namespaced
  versions:
    - name: v1beta1
      served: true
      storage: true
      schema:
        openAPIV3Schema:
          type: object
          properties:
            spec:
              type: object
              properties:
                connectCluster:
                  type: string
                  description: "The KafkaConnect cluster to deploy this connector to"
                  default: "kafka-connect-cluster"
                
                # MySQL Source Configuration
                mysql:
                  type: object
                  properties:
                    hostname:
                      type: string
                      description: "MySQL server hostname"
                    port:
                      type: integer
                      description: "MySQL server port"
                      default: 3306
                    database:
                      type: string
                      description: "MySQL database name"
                    username:
                      type: string
                      description: "MySQL username"
                    awsSecretRef:
                      type: object
                      properties:
                        secretName:
                          type: string
                          description: "AWS Secrets Manager secret name for MySQL credentials"
                        region:
                          type: string
                          description: "AWS region for the secret"
                          default: "eu-west-1"
                        passwordKey:
                          type: string
                          description: "Key in the secret for MySQL password"
                          default: "password"
                    tables:
                      type: array
                      items:
                        type: string
                      description: "List of tables to replicate (empty = all tables)"
                    binlogPosition:
                      type: string
                      enum: ["earliest", "latest", "specific"]
                      description: "Binlog starting position"
                      default: "latest"
                  required:
                    - hostname
                    - database
                    - username
                    - awsSecretRef
                
                # S3 Sink Configuration
                s3:
                  type: object
                  properties:
                    bucket:
                      type: string
                      description: "S3 bucket name"
                    prefix:
                      type: string
                      description: "S3 key prefix"
                      default: "mysql-cdc"
                    region:
                      type: string
                      description: "S3 bucket region"
                      default: "eu-west-1"
                    format:
                      type: string
                      enum: ["avro", "json", "parquet"]
                      description: "Output format for S3 files"
                      default: "avro"
                    flushSize:
                      type: integer
                      description: "Number of records per S3 file"
                      default: 1000
                    rotateIntervalMs:
                      type: integer
                      description: "Time interval for file rotation (milliseconds)"
                      default: 300000
                  required:
                    - bucket
              required:
                - mysql
                - s3
            
            status:
              type: object
              properties:
                phase:
                  type: string
                  enum: ["Pending", "Running", "Failed", "Paused"]
                  description: "Current phase of the MySQL to S3 pipeline"
                connectorName:
                  type: string
                  description: "Name of the deployed KafkaConnect connector"
                lastSyncTime:
                  type: string
                  format: date-time
                  description: "Last successful sync timestamp"
                recordsProcessed:
                  type: integer
                  description: "Total number of records processed"
                conditions:
                  type: array
                  items:
                    type: object
                    properties:
                      type:
                        type: string
                        description: "Condition type (Ready, Error, Syncing)"
                      status:
                        type: string
                        description: "Condition status (True, False, Unknown)"
                      lastTransitionTime:
                        type: string
                        format: date-time
                        description: "Last time the condition transitioned"
                      reason:
                        type: string
                        description: "Reason for the condition"
                      message:
                        type: string
                        description: "Human-readable message about the condition"
      
      subresources:
        status: {}
      
      additionalPrinterColumns:
        - name: Phase
          type: string
          jsonPath: ".status.phase"
          description: "Current phase of the pipeline"
        - name: MySQL Host
          type: string
          jsonPath: ".spec.mysql.hostname"
          description: "MySQL server hostname"
        - name: S3 Bucket
          type: string
          jsonPath: ".spec.s3.bucket"
          description: "Target S3 bucket"
        - name: Records Processed
          type: integer
          jsonPath: ".status.recordsProcessed"
          description: "Total records processed"
        - name: Age
          type: date
          jsonPath: ".metadata.creationTimestamp"
