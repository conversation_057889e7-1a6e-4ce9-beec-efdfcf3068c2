apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  name: kafkaconnectpipelines.kafka.careem.com
spec:
  group: kafka.careem.com
  names:
    plural: kafkaconnectpipelines
    singular: kafkaconnectpipeline
    kind: KafkaConnectPipeline
    shortNames:
      - kcp
  scope: Namespaced
  versions:
    - name: v1beta1
      served: true
      storage: true
      schema:
        openAPIV3Schema:
          type: object
          properties:
            spec:
              type: object
              properties:
                connectCluster:
                  type: string
                  description: "The KafkaConnect cluster this pipeline is associated with."
                mode:
                  type: string
                  enum: [ "source", "sink", "flow-through" ]
                  description: "Pipeline mode: source-only, sink-only, or flow-through."
                source:
                  type: object
                  properties:
                    endpointRef:
                      type: string
                      description: "Reference to a KafkaConnectEndpoint CR."
                    configOverrides:
                      type: object
                      additionalProperties: true
                      description: "Overrides for source connector configuration."
                target:
                  type: object
                  properties:
                    endpointRef:
                      type: string
                      description: "Reference to a KafkaConnectEndpoint CR."
                    configOverrides:
                      type: object
                      additionalProperties: true
                      description: "Overrides for sink connector configuration."
                transforms:
                  type: array
                  items:
                    type: string
                    description: "List of transformation names referenced from KafkaConnectTransform CRD."
            status:
              type: object
{{/*              x-kubernetes-preserve-unknown-fields: true*/}}
              properties:
                phase:
                  type: string
                  description: "Current phase of the pipeline (Pending, Running, Failed, Succeeded)."
                conditions:
                  type: array
                  items:
                    type: object
                    properties:
                      type:
                        type: string
                        description: "Condition type (Ready, NotReady, Error)."
                      status:
                        type: string
                        description: "Current status of the condition."
                      lastChecked:
                        type: string
                        format: date-time
                        description: "Timestamp of last status update."
                      message:
                        type: string
                        description: "Additional details about the condition."
      subresources:
        status: { }
      additionalPrinterColumns:
        - name: Mode
          type: string
          jsonPath: ".spec.mode"
          description: "Type of pipeline (source, sink, or flow-through)."
        - name: Cluster
          type: string
          jsonPath: ".spec.connectCluster"
          description: "Associated KafkaConnect cluster."
        - name: Status
          type: string
          jsonPath: ".status.phase"
          description: "Current pipeline status."
        - name: Last Checked
          type: string
          format: date-time
          jsonPath: ".status.conditions[0].lastChecked"
          description: "Timestamp of last status check."
