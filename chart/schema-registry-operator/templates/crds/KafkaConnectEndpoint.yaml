apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  name: kafkaconnectendpoints.kafka.careem.com
spec:
  group: kafka.careem.com
  names:
    plural: kafkaconnectendpoints
    singular: kafkaconnectendpoint
    kind: KafkaConnectEndpoint
    shortNames:
      - kce
  scope: Namespaced
  versions:
    - name: v1beta1
      served: true
      storage: true
      schema:
        openAPIV3Schema:
          type: object
          properties:
            spec:
              type: object
              properties:
                connectCluster:
                  type: string
                  description: "The KafkaConnect cluster this endpoint is associated with."
                type:
                  type: string
                  enum: [ "mysql", "postgres", "mongodb", "dynamodb", "elasticsearch", "http", "s3" ]
                  description: "The type of the source or sink (e.g., MySQL, DynamoDB, Elasticsearch)."
                config:
                  type: object
                  additionalProperties: true
                  description: "Connector-specific configuration settings."
                awsSecretRef:
                  type: object
                  properties:
                    secretName:
                      type: string
                      description: "AWS Secrets Manager secret storing credentials."
                    region:
                      type: string
                      description: "AWS region where the secret is stored."
                    key:
                      type: string
                      description: "Key within the secret JSON to extract credentials."
                healthCheck:
                  type: object
                  properties:
                    intervalSeconds:
                      type: integer
                      description: "Frequency of health checks in seconds."
                    enabled:
                      type: boolean
                      default: true
                      description: "Enable or disable endpoint health checks."
              required:
                - connectCluster
                - type
            status:
              type: object
{{/*              x-kubernetes-preserve-unknown-fields: true*/}}
              properties:
                conditions:
                  type: array
                  items:
                    type: object
                    properties:
                      type:
                        type: string
                        description: "Condition type (e.g., Ready, NotReady, Error)."
                      status:
                        type: string
                        description: "Current status of the condition."
                      lastChecked:
                        type: string
                        format: date-time
                        description: "Timestamp of last health check."
                      message:
                        type: string
                        description: "Additional details about the condition."
      subresources:
        status: { }
      additionalPrinterColumns:
        - name: Type
          type: string
          jsonPath: ".spec.type"
          description: "The type of the source or sink."
        - name: Cluster
          type: string
          jsonPath: ".spec.connectCluster"
          description: "The KafkaConnect cluster associated with this endpoint."
        - name: Status
          type: string
          jsonPath: ".status.conditions[0].status"
          description: "Current health status of the endpoint."
        - name: Last Checked
          type: string
          format: date-time
          jsonPath: ".status.conditions[0].lastChecked"
          description: "Timestamp of the last health check."

