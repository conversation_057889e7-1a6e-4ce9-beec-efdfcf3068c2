replicaCount: 1

image:
  imageName: "public.ecr.aws/s9o7c2r6/kubectl:latest"
  pullPolicy: IfNotPresent
  imagePullSecrets: ""

podAnnotations: {} 

resources:
  # {}
  limits:
    cpu: 200m
    memory: 512Mi
  requests:
    cpu: 200m
    memory: 256Mi

podSecurityContext:
  {}
  # fsGroup: 2000

securityContext:
  {}

nodeSelector: {}

tolerations: []

affinity: {}

env:
  - name: SCHEMA_REGISTRY_ENDPOINT
    value: http://schema-registry.kafka.dev.careem-rh.com
  - name: SCHEMA_REGISTRY_BASIC_AUTH_ENABLED
    value: "false"
  - name: SCHEMA_REGISTRY_USERNAME
    value: ""
  - name: SCHEMA_REGISTRY_PASSWORD
    value: ""
  - name: SCHEMA_REGISTRY_INSECURE
    value: "false"
