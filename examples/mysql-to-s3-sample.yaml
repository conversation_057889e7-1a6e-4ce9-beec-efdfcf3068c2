apiVersion: kafka.careem.com/v1beta1
kind: MySQLToS3
metadata:
  name: user-events-pipeline
  namespace: data-platform
spec:
  connectCluster: "kafka-connect-cluster"
  
  # MySQL Source Configuration
  mysql:
    hostname: "mysql.careem-prod.com"
    port: 3306
    database: "user_service"
    username: "kafka_user"
    awsSecretRef:
      secretName: "mysql-kafka-credentials"
      region: "eu-west-1"
      passwordKey: "password"
    tables:
      - "users"
      - "user_events"
      - "user_preferences"
    binlogPosition: "latest"
  
  # S3 Sink Configuration
  s3:
    bucket: "careem-data-lake"
    prefix: "mysql-cdc/user-service"
    region: "eu-west-1"
    format: "avro"
    flushSize: 1000
    rotateIntervalMs: 300000  # 5 minutes
  
  # Advanced Configuration
  advanced:
    tasksMax: 2
    pollIntervalMs: 1000
    maxBatchSize: 2048
    enableSchemaHistory: true
    deadLetterQueue:
      enabled: true
      topic: "data-platform.user-events-pipeline.dlq"
