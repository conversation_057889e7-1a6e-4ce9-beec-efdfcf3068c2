from prometheus_client import start_http_server, Counter
import logging
from config import Config
from read_env import ReadEnv
Config().load_configs()
ReadEnv()

logging.basicConfig(
    format='%(asctime)s %(levelname)-8s %(message)s',
    level=logging.INFO,
    datefmt='%Y-%m-%d %H:%M:%S'
)

class KafkaMetricsExporter:
    def __init__(self):
        self.kafka_metric_schema_operation = Counter('kafka_k8s_schema_registry_operator_operations', 
                                                   'Kafka schema registry operation', 
                                                      [
                                                       'operation',
                                                       'environment',
                                                       'k8s_resource_name',
                                                       'k8s_namespace',
                                                       'service_name',
                                                       'schema_migrated',
                                                       'schema_subject', 
                                                       'schema_last_version', 
                                                       'schema_versions', 
                                                       'schema_type', 
                                                       'schema_compatibility',
                                                       'ready', 
                                                       'warning', 
                                                       'exception', 
                                                       'success'
                                                       ])
        self.debug = True
        self.port_number = 8000
        self.read_kafaka_config = ReadEnv()
        self.run()

    def update_metrics(self,schema_dict_label):

        # Initiate the Exporter metrics
        logging.info("Updating metrics")
        self.kafka_metric_schema_operation.labels(
            operation=schema_dict_label['operation'],
            environment=Config.schema_registry_env,
            k8s_resource_name=schema_dict_label['k8s_resource_name'],
            k8s_namespace=schema_dict_label['k8s_namespace'],
            service_name=schema_dict_label['service_name'],
            schema_migrated=schema_dict_label['schema_migrated'],
            schema_subject=schema_dict_label['schema_subject'],
            schema_last_version=schema_dict_label['schema_last_version'],
            schema_versions=schema_dict_label['schema_versions'],
            schema_type=schema_dict_label['schema_type'],
            schema_compatibility=schema_dict_label['schema_compatibility'],
            ready=schema_dict_label['ready'],
            warning=schema_dict_label['warning'],
            exception=schema_dict_label['exception'],
            success=schema_dict_label['success']).inc()

    def run(self):
        logging.info(f"Starting exporter ...")
        logging.info("=" * len(f"Exporter started succssfully, listening on port {self.port_number}"))
        logging.info(f"Exporter started succssfully, listening on port {self.port_number}")
        logging.info("=" * len(f"Exporter started succssfully, listening on port {self.port_number}"))
        start_http_server(self.port_number)