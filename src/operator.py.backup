import kopf
import logging
from config import Config
from read_env import ReadEnv
from schema_registry_apis import SchemeRegistryAPI

Config().load_configs()
ReadEnv()
sr_api = SchemeRegistryAPI()

from operations import Operations
ops = Operations()


@kopf.on.startup()
def configure(settings: kopf.OperatorSettings, **_):
    settings.posting.level = logging.DEBUG
    settings.posting.enabled = True
    settings.persistence.finalizer = 'careem.com/schema-registry-operator'
    settings.watching.server_timeout = 300 # https://github.com/nolar/kopf/issues/232#issuecomment-704662003
    settings.watching.client_timeout = 300

@kopf.on.create("kafka.careem.com", "v1beta1", "schemaregistryschemas", timeout=3, retries=3, param="create")
@kopf.on.update("kafka.careem.com", "v1beta1", "schemaregistryschemas", timeout=3, retries=3, param="update")
async def create_fn(name, namespace, body, spec, param, **kwargs):
    if param == 'update':
        logging.info(f"Received update event for {name} in namespace {namespace}")
        return ops.update_schema(name=name, namespace=namespace, body=body, spec=spec, kwargs=kwargs)
    elif param == 'create':
        logging.info(f"Received create event for {name} in namespace {namespace}")
        return ops.create_schema(name=name, namespace=namespace, body=body, spec=spec)

def resource_is_deleted(event, **_):
    return event['type'] == 'DELETED'

@kopf.on.event("kafka.careem.com", "v1beta1", "schemaregistryschemas", when=resource_is_deleted)
async def delete(namespace,body, spec, **kwargs):
    return ops.delete_schema( namespace,body, spec)

# @kopf.on.resume("kafka.careem.com", "v1beta1", "SchemaRegistrySchema")
# async def ignore_resume(name, namespace, body, spec, **kwargs):
#     return

