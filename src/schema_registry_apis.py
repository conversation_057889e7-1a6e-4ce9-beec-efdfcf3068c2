import requests
from requests.adapters import HTT<PERSON><PERSON>pter
from urllib3.util import Re<PERSON>
from http import HTT<PERSON>tatus
from urllib3.exceptions import InsecureRequestWarning
requests.packages.urllib3.disable_warnings(category=InsecureRequestWarning)
requests.packages.urllib3.util.ssl_.DEFAULT_CIPHERS += ':HIGH:!DH:!aNULL'
from config import Config
# from read_env import ReadEnv
import logging

class SchemeRegistryAPI:

    def __init__(self):
        self.session = None
        self.schema_registry_endpoint = Config.schema_registry_endpoint
        self.headers = {'Content-Type': 'application/vnd.schemaregistry.v1+json'}
        self.get_session()

    def get_session(self):
        try:
            retry_strategy = Retry(
                total=4,
                backoff_factor=2,
                status_forcelist=[429, 500],
            )
            # Create an HTTP adapter with the retry strategy and mount it to session
            adapter = HTTPAdapter(max_retries=retry_strategy)

            session = requests.Session()
            session.mount('http://', adapter)
            logging.info("Establishing a new connection with Schema Registry")
            self.session = session
            self.session.verify = Config.schema_registry_insecure

            if Config.basic_auth_enabled:
                req = session.get(self.schema_registry_endpoint, auth=(Config.basic_auth_username, Config.basic_auth_password))
            else:
                req = session.get(self.schema_registry_endpoint)

            if req.status_code == HTTPStatus.OK:
                logging.info(f"connected successfully, status_code: {req.status_code}")
            else:
                print(f"ERROR -- Failed to connect to Schema-Registry '{self.schema_registry_endpoint}'; status_code: {req.status_code}, reason: {req.reason}\n")
                logging.error(f"Failed to connect to Schema-Registry, status_code: {req.status_code}")
                exit(1)
        except requests.exceptions.Timeout as e:
            print(f"ERROR -- Failed to connect to Schema-Registry '{self.schema_registry_endpoint}'; Timeout\n")
            logging.error(f"Failed to connect to Schema-Registry '{self.schema_registry_endpoint}'; Timeout\n")
            logging.error(e)
            raise SystemExit(f"> {e}")
        except requests.exceptions.TooManyRedirects as e:
            print(f"ERROR -- Failed to connect to Schema-Registry '{self.schema_registry_endpoint}'; Too Many Redirects\n")
            logging.error(f"Failed to connect to Schema-Registry '{self.schema_registry_endpoint}'; Too Many Redirects\n")
            logging.error(e)
            raise SystemExit(f"> {e}")
        except Exception as e:
            print(f"ERROR -- Failed to connect to Schema-Registry '{self.schema_registry_endpoint}'; Connection Failed\n")
            logging.error(f"Failed to connect to Schema-Registry '{self.schema_registry_endpoint}'; Connection Failed\n")
            logging.error(e)
            raise SystemExit(f"> {e}")

    def result_set(self, url, req, method="POST"):
        if req.status_code == 200:
            logging.info(f"Request {method} {url} is successful, status_code: {req.status_code}, reason: {req.reason}")
        else:
            logging.error(f"Request {method} {url} is failed, status_code: {req.status_code}, reason: {req.reason}")
            # logging.error(f"Request {method} {url} is failed, status_code: {req.status_code}, reason: {req.reason} \nHeaders:{req.request.headers} \nURL:{req.request.url}")

        return {'status_code': req.status_code, 'reason': req.reason, 'response': req.text}

    # def get_headers(self):
    #     headers = {'Content-Type': 'application/vnd.schemaregistry.v1+json'}
    #     return headers

    def create_schema(self, schema_json, subject, schema_type="AVRO"):

        if not self.session:
            self.get_session()

        url = self.schema_registry_endpoint + f"/subjects/{subject}/versions"
        req =  self.session.post(url=url,
                                headers=self.headers,
                                json={
                                        "schema": schema_json,
                                        "schemaType": schema_type,
                                      })
        return self.result_set(url, req)

    def get_schema_versions(self, subject):

        if not self.session:
            self.get_session()

        url = self.schema_registry_endpoint + f"/subjects/{subject}/versions"
        req =  self.session.get(url=url, headers=self.headers)

        return self.result_set(url, req, method="GET")

    def get_schema(self, subject, version="latest"):

        if not self.session:
            self.get_session()

        url = self.schema_registry_endpoint + f"/subjects/{subject}/versions/{version}"
        req =  self.session.get(url=url, headers=self.headers)

        return self.result_set(method="GET", url=url, req=req)

    def delete_schema(self, subject, version="latest", hard_delete=False):

        if not self.session:
            self.get_session()

        if hard_delete:
            url = self.schema_registry_endpoint + f"/subjects/{subject}/versions/{version}" + "?permanent=true"
        else:
            url = self.schema_registry_endpoint + f"/subjects/{subject}/versions/{version}"

        req =  self.session.delete(url=url, headers=self.headers)
        return self.result_set(method="DELETE", url=url, req=req)

    def delete_all_schemas(self, subject, hard_delete=False):

        if not self.session:
            self.get_session()

        if hard_delete:
            url = self.schema_registry_endpoint + f"/subjects/{subject}" + "?permanent=true"
        else:
            url = self.schema_registry_endpoint + f"/subjects/{subject}"

        req =  self.session.delete(url=url, headers=self.headers)
        return self.result_set(method="DELETE", url=url, req=req)

    def get_schema_compatibility(self, subject, schema_json):

        if not self.session:
            self.get_session()

        url = self.schema_registry_endpoint + f"/compatibility/subjects/{subject}/versions/latest"
        req =  self.session.post(url=url,
                                 headers=self.headers,
                                 json={"schema": schema_json}
                                 )

        return self.result_set(method="GET", url=url, req=req)


    def update_schema_compatibility(self, subject, compatibility):
        if not self.session:
            self.get_session()

        url = self.schema_registry_endpoint + f"/config/{subject}"
        req =  self.session.put(url=url,
                                headers=self.headers,
                                json={
                                        "compatibility": compatibility,
                                      }
                                )
        return self.result_set(url=url, req=req, method="PUT")
