import logging

from kubernetes import config as k8s_config
from kubernetes.config import ConfigException


class Config:

    def load_configs(self):
        self.set_logging_config()
        Config.schema_registry_endpoint = None
        Config.basic_auth_enabled = False
        Config.basic_auth_username = None
        Config.basic_auth_password = None
        Config.schema_registry_insecure = False
        Config.schema_delete_operations_hard_delete = False
        Config.schema_registry_env = None
        Config.allow_schema_delete_annotation = 'careem.com/enable-schema-delete'
        Config.managed_schema_annotation = 'careem.com/managed'

        # Try to load in-cluster config, fall back to kube config if it fails
        try:
            k8s_config.load_incluster_config()
            logging.info("Loaded in-cluster Kubernetes configuration.")
        except ConfigException:
            k8s_config.load_kube_config()
            logging.info("Loaded kubeconfig from file.")

    @staticmethod
    def set_logging_config():
        logging.basicConfig(
            format='%(asctime)s %(levelname)-8s %(message)s',
            level=logging.INFO,
            datefmt='%Y-%m-%d %H:%M:%S'
        )
