import kopf
import json
import logging
from config import Config
from kubernetes import client as k8s_client
from exporter import  KafkaMetricsExporter
metricsExporter=KafkaMetricsExporter()
# from kubernetes import config
from datetime import datetime
from jsonpath_ng import parse

from schema_registry_apis import SchemeRegistryAPI
sr_api = SchemeRegistryAPI()

class Operations:
    
    def __init__(self):
        pass
    
    # def k8s_authenticate(self):
    #     # config.load_kube_config()
    #     config.load_incluster_config()
    
    def patch_schema(self, name, namespace, body):
        # self.k8s_authenticate()

        from constants import CRD_GROUP, CRD_VERSION, CRD_PLURAL

        custom_api = k8s_client.CustomObjectsApi()
        patch = custom_api.patch_namespaced_custom_object(
            group=CRD_GROUP,
            version=CRD_VERSION,
            plural=CRD_PLURAL,
            namespace=namespace,
            name=name,
            body=body
        )
        return patch
    
    def create_schema(self, name, namespace, body, spec):
        ready = False
        warning = ""
        exception = ""
        id_ = " "
        versions = []  
        latest_version = ""
        success=False
        
        sr_api.get_session()

        # Validate the Schema Json
        try:
            json.loads(spec['schema'])
            logging.info(f"Schema with '{spec['subject']}' is valid")
        except Exception as e:
            logging.error(f"Schema with '{spec['subject']}' is NOT valid, {e}")
            exception = f"Schema with '{spec['subject']}' is NOT valid .. failed to load Json , {e}"
            warning = "invalid_json"
            schema_label_dic={
                'operation' : 'create',
                'k8s_resource_name' : body.get('metadata').get('name'),
                'k8s_namespace' : namespace,
                'service_name' : body.get('metadata').get('labels').get('service_name','unknown'),
                'schema_migrated' : body.get('metadata').get('labels').get('migrated','unknown'),
                'schema_subject' : spec['subject'],
                'schema_last_version' : latest_version,
                'schema_versions' : versions,
                'schema_type' : spec['schemaType'],
                'schema_compatibility' : spec['compatibility'],
                'ready' : ready,
                'warning' : warning,
                'exception' : exception,
                'success' : success
            }
            metricsExporter.update_metrics(schema_label_dic)
            return {
                # 'id': id_,
                'ready': ready,
                'warning': warning,
                'exception': exception
                # 'versions': versions,
                # 'latest_version': latest_version
            }

        try:
            self.__check_subject_existence(spec, body['metadata']['uid'])
        except Exception as e:
            warning = "duplicate_subject"
            exception = f"{e} .. Registering the new schema is skipped"
            logging.error(e)
            kopf.exception(
                body,
                reason='Failed',
                message=f"Got exception while validating the schema definition: {e}"
            )

            schema_label_dic={
                'operation' : 'create',
                'k8s_resource_name' : body.get('metadata').get('name'),
                'k8s_namespace' : namespace,
                'service_name' : body.get('metadata').get('labels').get('service_name','unknown'),
                'schema_migrated' : body.get('metadata').get('labels').get('migrated','unknown'),
                'schema_subject' : spec['subject'],
                'schema_last_version' : latest_version,
                'schema_versions' : versions,
                'schema_type' : spec['schemaType'],
                'schema_compatibility' : spec['compatibility'],
                'ready' : ready,
                'warning' : warning,
                'exception' : exception,
                'success' : success
            }
            metricsExporter.update_metrics(schema_label_dic)
            # If the schema already exists, don't override the status with empty strings.
            return {
                # 'id': id_,
                'ready': ready,
                'warning': warning,
                'exception': exception,
                # 'versions': versions,
                # 'latest_version': latest_version
            }

        try:
            # 1. Check if the schema exists.
            logging.info(f"Checking if the subject '{spec['subject']}' already exists")
            subject_check_req = sr_api.get_schema(subject=spec['subject'])
            if subject_check_req['status_code']  == 200:
                logging.info(f"Subject '{spec['subject']}' already exists, creating a new schema version")
                    
                # 1.1 Test schema compatability
                logging.info(f"Doing compatibility check for Subject '{spec['subject']}'")
                test_compatability_req = sr_api.get_schema_compatibility(spec['subject'],spec['schema'])
                if test_compatability_req['status_code']  == 200:
                    logging.info(f"Schema is compatible with the previous versions")
                else:
                    warning = "not_compatible (registering skipped)"
                    exception = f"Schema with subject '{spec['subject']}' is NOT compatible with the previous versions, Schema compatability test failed .. Registering the new schema version is skipped"
                    logging.error(f"Schema with subject '{spec['subject']}' is NOT compatible with the previous versions")
                    kopf.event(
                        body,
                        type='Normal',
                        reason='Failed',
                        message=f"Schema compatability test failed .. Registering the new schema version is skipped"
                    )
                    schema_label_dic={
                        'operation' : 'create',
                        'k8s_resource_name' : body.get('metadata').get('name'),
                        'k8s_namespace' : namespace,
                        'service_name' : body.get('metadata').get('labels').get('service_name','unknown'),
                        'schema_migrated' : body.get('metadata').get('labels').get('migrated','unknown'),
                        'schema_subject' : spec['subject'],
                        'schema_last_version' : latest_version,
                        'schema_versions' : versions,
                        'schema_type' : spec['schemaType'],
                        'schema_compatibility' : spec['compatibility'],
                        'ready' : ready,
                        'warning' : warning,
                        'exception' : exception,
                        'success' : success
                    }
                    metricsExporter.update_metrics(schema_label_dic)
                    # If the schema already exists, don't override the status with empty strings.
                    return {
                        # 'id': id_,
                        'ready': ready,
                        'warning': warning,
                        'exception': exception,
                        # 'versions': versions,
                        # 'latest_version': latest_version
                    }
            
            elif subject_check_req['status_code']  == 404:
                logging.info(f"Subject '{spec['subject']}' does not exist, creating a new schema")
        
            # 2. Register schema
            res = sr_api.create_schema(schema_json=spec['schema'], subject=spec['subject'], schema_type=spec['schemaType'])
            if res['status_code'] == 200:
                logging.info(f"Schema with subject '{spec['subject']}' created successfully")
                kopf.event(
                    body,
                    type='Normal',
                    reason='Created',
                    message=f"Schema with subject '{spec['subject']}' created successfully"
                )
                ready = True
                warning = ""
                id_ = str(json.loads(res['response']).get('id'))
                
                # Get the schema versions
                schema_versions_req = sr_api.get_schema_versions(subject=spec['subject'])
                if schema_versions_req['status_code']  == 200:
                    versions = json.loads(schema_versions_req['response'])
                    logging.info(f"Got schema versions successfully for subject Subject '{spec['subject']}'")
                else:
                    logging.info(f"Failed to get schema versions for Subject '{spec['subject']}'")
                    warning = "Failed to get schema versions"
                    ready = False
                # Get the schema last version
                schema_versions_req = sr_api.get_schema(subject=spec['subject'], version="latest")
                if schema_versions_req['status_code']  == 200:
                    logging.info(f"Got schema latest version successfully for Subject '{spec['subject']}'")
                    latest_version = json.loads(schema_versions_req['response']).get('version')
                    # schema_type = json.loads(schema_versions_req['response']).get('schemaType')
                else:
                    logging.info(f"Failed to get schema latest version for Subject '{spec['subject']}'") 
                    warning = "Failed to get schema latest version"
                    ready = False
                # Update compatability level
                update_schema_compatibility = sr_api.update_schema_compatibility(subject=spec['subject'], compatibility=spec['compatibility'])
                if update_schema_compatibility['status_code']  == 200:
                    logging.info(f"compatibility updated successfully for Subject '{spec['subject']}'")
                else:
                    logging.info(f"Failed to update compatibility, '{spec['subject']}'")
                    exception = f"(failed to update compatability, {update_schema_compatibility['status_code']} {update_schema_compatibility['reason']})"
                    warning = "Failed to update compatibility"
                    ready = False
                    kopf.event(
                        body,
                        type='Normal',
                        reason='Failed',
                        message=f"Failed to update compatibility: {update_schema_compatibility['status_code']}, reason: {update_schema_compatibility['reason']}, response: {update_schema_compatibility['response']}"
                    )
            elif res['status_code'] == 409:
                warning = f"not_compatible (registering skipped)"
                exception = f"{json.loads(res['response']).get('error_code')}, {json.loads(res['response']).get('message')}"
                logging.error(f"Schema with {spec['subject']} is NOT compatible with the previous versions")
                kopf.event(
                    body,
                    type='Normal',
                    reason='Failed',
                    message=f"Schema compatability test failed .. Registering the new schema version is skipped"
                )
                schema_label_dic={
                        'operation' : 'create',
                        'k8s_resource_name' : body.get('metadata').get('name'),
                        'k8s_namespace' : namespace,
                        'service_name' : body.get('metadata').get('labels').get('service_name','unknown'),
                        'schema_migrated' : body.get('metadata').get('labels').get('migrated','unknown'),
                        'schema_subject' : spec['subject'],
                        'schema_last_version' : latest_version,
                        'schema_versions' : versions,
                        'schema_type' : spec['schemaType'],
                        'schema_compatibility' : spec['compatibility'],
                        'ready' : ready,
                        'warning' : warning,
                        'exception' : exception,
                        'success' : success
                    }
                metricsExporter.update_metrics(schema_label_dic)
                return {
                    'ready': ready,
                    'warning': warning,
                    'exception': exception,
                }
            elif res['status_code'] == 422:
                warning = f"Invalid schema"
                exception = f"{json.loads(res['response']).get('error_code')}, {json.loads(res['response']).get('message')}"
                logging.error(f"Schema with {spec['subject']} is Invalid")
                kopf.event(
                    body,
                    type='Normal',
                    reason='Failed',
                    message=f"Schema with {spec['subject']} is Invalid"
                )
                schema_label_dic={
                        'operation' : 'create',
                        'k8s_resource_name' : body.get('metadata').get('name'),
                        'k8s_namespace' : namespace,
                        'service_name' : body.get('metadata').get('labels').get('service_name','unknown'),
                        'schema_migrated' : body.get('metadata').get('labels').get('migrated','unknown'),
                        'schema_subject' : spec['subject'],
                        'schema_last_version' : latest_version,
                        'schema_versions' : versions,
                        'schema_type' : spec['schemaType'],
                        'schema_compatibility' : spec['compatibility'],
                        'ready' : ready,
                        'warning' : warning,
                        'exception' : exception,
                        'success' : success
                    }
                metricsExporter.update_metrics(schema_label_dic)                
                return {
                    'ready': ready,
                    'warning': warning,
                    'exception': exception,
                }
            else:
                warning = f"failed to create schema, {res['status_code']}, {res['response']}"
                exception = f"{json.loads(res['response']).get('error_code')}, {json.loads(res['response']).get('message')}"
                logging.error(f"Failed to create Schema with subject {spec['subject']}")
                kopf.event(
                    body,
                    type='Normal',
                    reason='Failed',
                    message=f"Failed to create Schema with subject {spec['subject']}"
                )
                schema_label_dic={
                        'operation' : 'create',
                        'k8s_resource_name' : body.get('metadata').get('name'),
                        'k8s_namespace' : namespace,
                        'service_name' : body.get('metadata').get('labels').get('service_name','unknown'),
                        'schema_migrated' : body.get('metadata').get('labels').get('migrated','unknown'),
                        'schema_subject' : spec['subject'],
                        'schema_last_version' : latest_version,
                        'schema_versions' : versions,
                        'schema_type' : spec['schemaType'],
                        'schema_compatibility' : spec['compatibility'],
                        'ready' : ready,
                        'warning' : warning,
                        'exception' : exception,
                        'success' : success
                    }
                metricsExporter.update_metrics(schema_label_dic)                
                return {
                    'ready': ready,
                    'warning': warning,
                    'exception': exception,
                }

            # Patch the schem -> Update the lastUpdateTimeStamp
            try:
                self.patch_schema(name=name, namespace=namespace, body={"status": {"lastUpdateTimestamp": str(datetime.now().isoformat())}})
                logging.info(f"Schema '{name}' patched, 'status.lastUpdateTimestamp' is updated")
            except Exception as e:
                logging.warning(f"Failed to update 'status.lastUpdateTimestamp' -> {e}")

        except Exception as e:
            warning = "API Exception"
            exception = e
            kopf.event(
                body,
                type='Normal',
                reason='Failed',
                message=f"Got exception while creating the schema: {e}"
            )
        success=True    
        schema_label_dic={
                        'operation' : 'create',
                        'k8s_resource_name' : body.get('metadata').get('name'),
                        'k8s_namespace' : namespace,
                        'service_name' : body.get('metadata').get('labels').get('service_name','unknown'),
                        'schema_migrated' : body.get('metadata').get('labels').get('migrated','unknown'),
                        'schema_subject' : spec['subject'],
                        'schema_last_version' : latest_version,
                        'schema_versions' : versions,
                        'schema_type' : spec['schemaType'],
                        'schema_compatibility' : spec['compatibility'],
                        'ready' : ready,
                        'warning' : warning,
                        'exception' : exception,
                        'success' : success
                    }
        metricsExporter.update_metrics(schema_label_dic)
        return {
            'id': id_,
            'ready': ready,
            'warning': warning,
            'exception': exception,
            'versions': versions,
            'latest_version': latest_version,
        }


    def update_schema(self, name, namespace, body, spec, kwargs):
        ready = False
        warning = ""
        exception = ""
        id_ = " "
        versions = []
        latest_version = ""
        success=False
        
        sr_api.get_session()
        
        # Validate the Schema Json
        try:
            json.loads(spec['schema'])
            logging.info(f"Schema with '{spec['subject']}' is valid")
        except Exception as e:
            logging.error(f"Schema with '{spec['subject']}' is NOT valid, {e}")
            exception = f"Schema with '{spec['subject']}' is NOT valid .. failed to load Json , {e}"
            warning = "invalid_json"
            schema_label_dic={
                    'operation' : 'update',
                    'k8s_resource_name' : name,
                    'k8s_namespace' : namespace,
                    'service_name' : body.get('metadata').get('labels').get('service_name','unknown'),
                    'schema_migrated' : body.get('metadata').get('labels').get('migrated','unknown'),
                    'schema_subject' : spec['subject'],
                    'schema_last_version' : latest_version,
                    'schema_versions' : versions,
                    'schema_type' : spec['schemaType'],
                    'schema_compatibility' : spec['compatibility'],
                    'ready' : ready,
                    'warning' : warning,
                    'exception' : exception,
                    'success' : success
                }
            metricsExporter.update_metrics(schema_label_dic)            
            return {
                # 'id': id_,
                'ready': ready,
                'warning': warning,
                'exception': exception
                # 'versions': versions,
                # 'latest_version': latest_version
            }

        try:
            self.__error_if_changed(kwargs, ('spec', 'subject'))
        except Exception as e:
            ready = False
            warning = "update_blocked"
            exception = f"{e} .. the update operation is blocked!"
            logging.error(e)
            kopf.exception(
                body,
                reason='Failed',
                message=f"Got exception while validating the schema definition: {e}"
            )

            schema_label_dic = {
                'operation' : 'update',
                'k8s_resource_name' : name,
                'k8s_namespace' : namespace,
                'service_name' : body.get('metadata').get('labels').get('service_name','unknown'),
                'schema_migrated' : body.get('metadata').get('labels').get('migrated','unknown'),
                'schema_subject' : spec['subject'],
                'schema_last_version' : latest_version,
                'schema_versions' : versions,
                'schema_type' : spec['schemaType'],
                'schema_compatibility' : spec['compatibility'],
                'ready' : ready,
                'warning' : warning,
                'exception' : exception,
                'success' : success
            }
            metricsExporter.update_metrics(schema_label_dic)

            # if changes is not None:
            #     logging.info(f"Reverting changes on 'spec.subject' field {changes.old}")
            #     # self.patch_schema(name=name, namespace=namespace, body={"spec": {"subject": changes.old}})
            #     # kopf.adopt([{"spec": {"subject": changes.new}},{"spec": {"subject": changes.old}}])
            #     api = k8s_client.CustomObjectsApi()
            #     from src.constants import CRD_GROUP, CRD_VERSION, NAMESPACE, CRD_PLURAL
            #     api.patch_namespaced_custom_object(
            #         group=CRD_GROUP,
            #         version=CRD_VERSION,
            #         namespace=NAMESPACE,
            #         plural=CRD_PLURAL,
            #         name=body['metadata']['name'],
            #         body={"spec": {"subject": changes.old}}
            #     )

            # If the schema already exists, don't override the status with empty strings.
            return {
                # 'id': id_,
                'ready': ready,
                'warning': warning,
                'exception': exception,
                # 'versions': versions,
                # 'latest_version': latest_version
            }

        # 1. Check for Schemas versions soft deletion "defined as annotations"
        soft_delete_versions = []
        try:
            soft_delete_versions_raw = body.get('metadata').get('annotations').get('kafka.careem.com/soft-delete-versions')
            if soft_delete_versions_raw is not None:
                soft_delete_versions = json.loads(soft_delete_versions_raw)
        except KeyError:
            pass # Means the annotation does not exist .. Moving on.
        except json.decoder.JSONDecodeError:
            logging.warning(f"Wrong input in the 'kafka.careem.com/soft-delete-versions' annotation, '{soft_delete_versions_raw}' .. skipping handling the annotation")

        if soft_delete_versions:
            # Delete the schema versions
            try:
                for version in soft_delete_versions:
                    delete_schema_req = sr_api.delete_schema(subject=spec['subject'], version=version)
                    if delete_schema_req['status_code']  == 200:
                        logging.info(f"Schema is deleted successfully (subject: {spec['subject']} ,version: {version}), exit_code: {delete_schema_req['status_code']}, reason: {delete_schema_req['reason']}")
                        kopf.event(
                            body,
                            type='Normal',
                            reason='Deleted',
                            message=f"Schema is deleted successfully (subject: {spec['subject']} ,version: {version})"
                        )
                    else:
                        logging.error(f"Failed to delete schema, exit_code: {delete_schema_req['status_code']}, reason: {delete_schema_req['reason']}")
            except Exception as e:
                logging.warning(f"Got exception while deleting Schema, {e}")
        
        try:
            # 2. Test schema compatability
            test_compatability_req = sr_api.get_schema_compatibility(spec['subject'],spec['schema'])
            if test_compatability_req['status_code']  == 200:
                logging.info(f"Schema with subject {spec['subject']} is compatible with the previous versions")
            else:
                warning = "not_compatible (registering skipped)"
                logging.error(f"Schema with {spec['subject']} is NOT compatible with the previous versions")
                kopf.event(
                    body,
                    type='Normal',
                    reason='Failed',
                    message=f"Schema compatability test failed .. Registering the new schema version is skipped"
                )
                exception = f"Schema with subject '{spec['subject']}' is NOT compatible with the previous versions, Schema compatability test failed .. Registering the new schema version is skipped"
                schema_label_dic = {
                        'operation' : 'update',
                        'k8s_resource_name' : name,
                        'k8s_namespace' : namespace,
                        'service_name' : body.get('metadata').get('labels').get('service_name','unknown'),
                        'schema_migrated' : body.get('metadata').get('labels').get('migrated','unknown'),
                        'schema_subject' : spec['subject'],
                        'schema_last_version' : latest_version,
                        'schema_versions' : versions,
                        'schema_type' : spec['schemaType'],
                        'schema_compatibility' : spec['compatibility'],
                        'ready' : ready,
                        'warning' : warning,
                        'exception' : exception,
                        'success' : success
                    }
                metricsExporter.update_metrics(schema_label_dic)                
                return {
                    'ready': ready,
                    'warning': warning,
                    'exception': exception,
                }

            # 3. Register schema version
            
            res = sr_api.create_schema(schema_json=spec['schema'], subject=spec['subject'], schema_type=spec['schemaType'])
            if res['status_code'] == 200:
                kopf.event(
                    body,
                    type='Normal',
                    reason='Updated',
                    message='Schema updated successfully'
                )
                ready = True
                warning = ""
                id_ = str(json.loads(res['response']).get('id'))
            elif res['status_code'] == 409:
                warning = f"not_compatible (registering skipped)"
                exception = f"{json.loads(res['response']).get('error_code')}, {json.loads(res['response']).get('message')}"
                logging.error(f"Schema with {spec['subject']} is NOT compatible with the previous versions")
                kopf.event(
                    body,
                    type='Normal',
                    reason='Failed',
                    message=f"Schema compatability test failed .. Registering the new schema version is skipped"
                )
                schema_label_dic={
                        'operation' : 'update',
                        'k8s_resource_name' : body.get('metadata').get('name'),
                        'k8s_namespace' : namespace,
                        'service_name' : body.get('metadata').get('labels').get('service_name','unknown'),
                        'schema_migrated' : body.get('metadata').get('labels').get('migrated','unknown'),
                        'schema_subject' : spec['subject'],
                        'schema_last_version' : latest_version,
                        'schema_versions' : versions,
                        'schema_type' : spec['schemaType'],
                        'schema_compatibility' : spec['compatibility'],
                        'ready' : ready,
                        'warning' : warning,
                        'exception' : exception,
                        'success' : success
                    }
                metricsExporter.update_metrics(schema_label_dic)                
                return {
                    'ready': ready,
                    'warning': warning,
                    'exception': exception,
                }
            elif res['status_code'] == 422:
                warning = f"Invalid schema"
                exception = f"{json.loads(res['response']).get('error_code')}, {json.loads(res['response']).get('message')}"
                logging.error(f"Schema with {spec['subject']} is Invalid")
                kopf.event(
                    body,
                    type='Normal',
                    reason='Failed',
                    message=f"Schema with {spec['subject']} is Invalid"
                )
                schema_label_dic={
                        'operation' : 'update',
                        'k8s_resource_name' : name,
                        'k8s_namespace' : namespace,
                        'service_name' : body.get('metadata').get('labels').get('service_name','unknown'),
                        'schema_migrated' : body.get('metadata').get('labels').get('migrated','unknown'),
                        'schema_subject' : spec['subject'],
                        'schema_last_version' : latest_version,
                        'schema_versions' : versions,
                        'schema_type' : spec['schemaType'],
                        'schema_compatibility' : spec['compatibility'],
                        'ready' : ready,
                        'warning' : warning,
                        'exception' : exception,
                        'success' : success
                    }
                metricsExporter.update_metrics(schema_label_dic)                
                return {
                    'ready': ready,
                    'warning': warning,
                    'exception': exception,
                }
            else:
                warning = f"failed to create schema, {res['status_code']}, {res['response']}"
                exception = f"{json.loads(res['response']).get('error_code')}, {json.loads(res['response']).get('message')}"
                logging.error(f"Failed to create Schema with subject {spec['subject']}")
                kopf.event(
                    body,
                    type='Normal',
                    reason='Failed',
                    message=f"Failed to create Schema with subject {spec['subject']}"
                )
                schema_label_dic={
                        'operation' : 'update',
                        'k8s_resource_name' : name,
                        'k8s_namespace' : namespace,
                        'service_name' : body.get('metadata').get('labels').get('service_name','unknown'),
                        'schema_migrated' : body.get('metadata').get('labels').get('migrated','unknown'),
                        'schema_subject' : spec['subject'],
                        'schema_last_version' : latest_version,
                        'schema_versions' : versions,
                        'schema_type' : spec['schemaType'],
                        'schema_compatibility' : spec['compatibility'],
                        'ready' : ready,
                        'warning' : warning,
                        'exception' : exception,
                        'success' : success
                    }
                metricsExporter.update_metrics(schema_label_dic)                
                return {
                    'ready': ready,
                    'warning': warning,
                    'exception': exception,
                }
            
            
            # 3. Update compatability level
            update_schema_compatibility = sr_api.update_schema_compatibility(subject=spec['subject'], compatibility=spec['compatibility'])
            if update_schema_compatibility['status_code']  == 200:
                logging.info(f"compatibility updated successfully for Subject '{spec['subject']}'")
            else:
                logging.info(f"Failed to update compatibility, '{spec['subject']}'")
                exception = f"(failed to update compatability, {update_schema_compatibility['status_code']} {update_schema_compatibility['reason']})"
                warning = "failed to update compatability"
                ready = False
                kopf.event(
                    body,
                    type='Normal',
                    reason='Failed',
                    message=f"Failed to update compatibility: {update_schema_compatibility['status_code']}, reason: {update_schema_compatibility['reason']}, response: {update_schema_compatibility['response']}"
                )

            # Get the schema versions
            schema_versions_req = sr_api.get_schema_versions(subject=spec['subject'])
            if schema_versions_req['status_code']  == 200:
                versions = json.loads(schema_versions_req['response'])
                logging.info(f"Got schema versions successfully for subject Subject '{spec['subject']}', exit_code: {schema_versions_req['status_code']}, reason: {schema_versions_req['reason']}")
            else:
                logging.info(f"Failed to get schema versions for Subject '{spec['subject']}', exit_code: {schema_versions_req['status_code']}, reason: {schema_versions_req['reason']}")
                exception = f"Failed to get schema versions for Subject '{spec['subject']}', exit_code: {schema_versions_req['status_code']}, reason: {schema_versions_req['reason']}"
            
            # Get the schema last version
            schema_versions_req = sr_api.get_schema(subject=spec['subject'], version="latest")
            if schema_versions_req['status_code']  == 200:
                logging.info(f"Got schema latest version successfully for Subject '{spec['subject']}', exit_code: {schema_versions_req['status_code']}, reason: {schema_versions_req['reason']}")
                latest_version = json.loads(schema_versions_req['response']).get('version')
            else:
                logging.info(f"Failed to get schema latest version for Subject '{spec['subject']}', exit_code: {schema_versions_req['status_code']}, reason: {schema_versions_req['reason']}") 
                exception = f"Failed to get schema latest version for Subject '{spec['subject']}', exit_code: {schema_versions_req['status_code']}, reason: {schema_versions_req['reason']}"

            # Patch the schem -> Update the lastUpdateTimeStamp
            try:
                self.patch_schema(name=name, namespace=namespace, body={"status": {"lastUpdateTimestamp": str(datetime.now().isoformat())}})
                logging.info(f"Schema '{name}' patched, 'status.lastUpdateTimestamp' is updated")
            except Exception as e:
                logging.warning(f"Failed to update 'status.lastUpdateTimestamp' -> {e}")

        except Exception as e:
            warning = "API Exception"
            exception = e
            kopf.event(
                body,
                type='Normal',
                reason='Failed',
                message=f"Got exception while creating the schema: {e}"
            )
        success=True
        schema_label_dic={
                'operation' : 'update',
                'k8s_resource_name' : name,
                'k8s_namespace' : namespace,
                'service_name' : body.get('metadata').get('labels').get('service_name','unknown'),
                'schema_migrated' : body.get('metadata').get('labels').get('migrated','unknown'),
                'schema_subject' : spec['subject'],
                'schema_last_version' : latest_version,
                'schema_versions' : versions,
                'schema_type' : spec['schemaType'],
                'schema_compatibility' : spec['compatibility'],
                'ready' : ready,
                'warning' : warning,
                'exception' : exception,
                'success' : success
            }
        metricsExporter.update_metrics(schema_label_dic)            
        return {
            'id': id_,
            'ready': ready,
            'warning': warning,
            'exception': exception,
            'versions': versions,
            'latest_version': latest_version
        }

    
    def delete_schema(self, namespace, body, spec):
        ready = False
        warning = ""
        exception = ""
        versions = []
        latest_version = ""
        sr_api.get_session()
        success = False
        hard_delete = True
        
        # If schema has annotation "careem.com/managed": "false"
        if body['metadata'].get('annotations', {}).get(Config.allow_schema_delete_annotation, 'true') == 'false':
            msg = f"Skiping deleting schema '{body['metadata']['name']}' since it is annotated with '{Config.allow_schema_delete_annotation}=false' - (subject: {spec['subject']})"
            logging.info(msg)
            kopf.event(body, type='Normal', reason='Created', message=msg)
            success = True
            ready = body['status'].get('create_fn', {}).get('ready', False)
            hard_delete = False
            schema_label_dic={
                    'operation' : 'delete',
                    'k8s_resource_name' : body.get('metadata').get('name'),
                    'k8s_namespace' : namespace,
                    'service_name' : body.get('metadata').get('labels').get('service_name','unknown'),
                    'schema_migrated' : body.get('metadata').get('labels').get('migrated','unknown'),
                    'schema_subject' : spec['subject'],
                    'schema_last_version' : latest_version,
                    'schema_versions' : versions,
                    'schema_type' : spec['schemaType'],
                    'schema_compatibility' : spec['compatibility'],
                    'ready' : ready,
                    'warning' : "Schema delete skipped",
                    'exception' : msg,
                    'success' : success
                }
            metricsExporter.update_metrics(schema_label_dic)        
            return {
                'ready': ready,
                'warning': warning,
                'exception': msg,
            }        

        jsonpath_expression = parse('$.status.create_fn.warning')
        matches = jsonpath_expression.find(body)
        warning_message = None
        if matches:
            warning_message = matches[0].value

        if warning_message is not None and warning_message == 'duplicate_subject':
            msg = f"The incomplete schema '{body['metadata']['name']}' has been deleted successfully (subject: {spec['subject']})"
            logging.info(msg)
            kopf.event(body, type='Normal', reason='Deleted', message=msg)
            success = True
            hard_delete = False

        # Delete all the schemas versions
        if hard_delete:
            delete_schema_req = sr_api.delete_all_schemas(spec['subject'], hard_delete=Config.schema_delete_operations_hard_delete)
            if delete_schema_req['status_code']  == 200:
                logging.info(f"Schema is deleted successfully (subject: {spec['subject']})")
                kopf.event(
                    body,
                    type='Normal',
                    reason='Deleted',
                    message=f"Schema is deleted successfully (subject: {spec['subject']})"
                )
                success=True
                msg = f"Schema {body['metadata']['name']} deleted successfully"
            else:
                logging.error(f"Failed to delete schema, exit_code: {delete_schema_req['status_code']}, reason: {delete_schema_req['reason']}")
                msg = f"Failed to delete Schema {body['metadata']['name']}"
                warning = msg
                exception=f"Failed to delete schema, exit_code: {delete_schema_req['status_code']}, reason: {delete_schema_req['reason']}"

        schema_label_dic={
                'operation' : 'delete',
                'k8s_resource_name' : body.get('metadata').get('name'),
                'k8s_namespace' : namespace,
                'service_name' : body.get('metadata').get('labels').get('service_name','unknown'),
                'schema_migrated' : body.get('metadata').get('labels').get('migrated','unknown'),
                'schema_subject' : spec['subject'],
                'schema_last_version' : latest_version,
                'schema_versions' : versions,
                'schema_type' : spec['schemaType'],
                'schema_compatibility' : spec['compatibility'],
                'ready' : ready,
                'warning' : warning,
                'exception' : exception,
                'success' : success
            }
        metricsExporter.update_metrics(schema_label_dic)        
        return {'message': msg}

    # ================== Private Methods =================

    def __find_schema_by_subject(self, subject, current_uid):
        from constants import CRD_GROUP, CRD_VERSION, CRD_PLURAL, NAMESPACE

        api = k8s_client.CustomObjectsApi()

        # Get all schemas
        schemas = api.list_namespaced_custom_object(CRD_GROUP, CRD_VERSION, NAMESPACE, CRD_PLURAL)

        # Iterate over the schemas and find the one with the specific subject
        for schema in schemas['items']:
            logging.debug(f"schema: {schema}")

            if schema['metadata']['uid'] != current_uid and schema['spec']['subject'] == subject:
                return schema

        # Return None if no schema is found with the specific subject
        return None

    def __check_subject_existence(self, spec, current_uid):
        # 1. Check if the schema exists.
        logging.info(f"Checking if the subject '{spec['subject']}' already exists in the cluster resources")
        logging.debug(f"current_uid: {current_uid}")
        existing_schema = self.__find_schema_by_subject(spec['subject'], current_uid)

        if existing_schema is not None:
            raise kopf.PermanentError(f"The subject '{spec['subject']}' is already managed by another schema '{existing_schema['metadata']['name']}'")

        logging.info(f"Subject name '{spec['subject']}' is available")

    def __get_change_by_field(self, kwargs, field):
        logging.info(f"Checking {field} changes!")

        # Find the requested field changes
        return next((item for item in kwargs['diff'] if item.field == field), None)

    def __error_if_changed(self, kwargs, field):
        logging.info(f"Checking if '{field}' field changed!")

        change = self.__get_change_by_field(kwargs, field)

        if change is not None:
            raise kopf.PermanentError(f"Changing '{field}' field is prohibited!")

        logging.info(f"'{field}' field is not changed!")
