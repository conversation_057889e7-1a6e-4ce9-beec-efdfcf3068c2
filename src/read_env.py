import os
from config import Config

class ReadEnv:
    def __init__(self):
        self.read_env()
    
    def check_env(self, envs):
        """Checks if the ENV is defined

        Args:
            env (lst): takes a list of ENVs
        Return: dct
        """
        output = {
            "missing": False,
            "missing_envs": [],
            "error_msg": ""
        }
        for env in envs:
            try:
                os.environ[f'{env}']
            except KeyError:
                output['missing'] = True
                output['missing_envs'].append(env)
        
        if output['missing']:
            if len(output['missing_envs']) == 1:
                output['error_msg'] = f"INFO -- ENV: {output['missing_envs']} is missing"
            else:
                output['error_msg'] = f"INFO -- ENVs: {output['missing_envs']} are missing"

        return output


    def read_env(self):
        """
        Read environment variables
        """
        # Mandatory ENVs
        check_mandatory_envs = self.check_env(['SCHEMA_REGISTRY_ENDPOINT','SCHEMA_REGISTRY_ENV'])
        if check_mandatory_envs['missing']:
            print(check_mandatory_envs['error_msg'])
            exit(1)
        Config.schema_registry_endpoint = os.environ['SCHEMA_REGISTRY_ENDPOINT']
        Config.schema_registry_env=os.environ['SCHEMA_REGISTRY_ENV']
                
        # Filters + prometheus ENVs
        check_basic_auth_enabled = self.check_env(['SCHEMA_REGISTRY_BASIC_AUTH_ENABLED'])
        if not check_basic_auth_enabled['missing']:
            if (os.environ['SCHEMA_REGISTRY_BASIC_AUTH_ENABLED'] == 'true') or os.environ['SCHEMA_REGISTRY_BASIC_AUTH_ENABLED'] == 'True':
                Config.basic_auth_enabled = bool(os.environ['SCHEMA_REGISTRY_BASIC_AUTH_ENABLED'])
                check_basic_auth_creds = self.check_env(['SCHEMA_REGISTRY_USERNAME', 'SCHEMA_REGISTRY_PASSWORD'])
                if check_basic_auth_creds['missing']:
                    print(check_basic_auth_creds['error_msg'])
                    exit(1)
                Config.basic_auth_username = os.environ['SCHEMA_REGISTRY_USERNAME']
                Config.basic_auth_password = os.environ['SCHEMA_REGISTRY_PASSWORD']
    
        # Optional ENVs
        try:
            Config.schema_registry_insecure = bool(os.environ['SCHEMA_REGISTRY_INSECURE'])
        except (KeyError):
            pass