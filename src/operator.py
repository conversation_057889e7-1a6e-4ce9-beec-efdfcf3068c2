import kopf
import logging
from config import Config
from read_env import ReadEnv
from schema_registry_apis import SchemeRegistryAPI

Config().load_configs()
ReadEnv()
sr_api = SchemeRegistryAPI()

# Import existing Schema Registry operations
from operations import Operations
ops = Operations()

# Import new KafkaConnect operations
from kafka_connect_operations import KafkaConnectOperations
kc_ops = KafkaConnectOperations()


@kopf.on.startup()
def configure(settings: kopf.OperatorSettings, **_):
    settings.posting.level = logging.DEBUG
    settings.posting.enabled = True
    settings.persistence.finalizer = 'careem.com/kafka-connect-operator'
    settings.watching.server_timeout = 300
    settings.watching.client_timeout = 300
    logging.info("KafkaConnect Operator started successfully")


# ============================================================================
# Schema Registry Handlers (Existing functionality)
# ============================================================================

@kopf.on.create("kafka.careem.com", "v1beta1", "schemaregistryschemas", timeout=3, retries=3, param="create")
@kopf.on.update("kafka.careem.com", "v1beta1", "schemaregistryschemas", timeout=3, retries=3, param="update")
async def schema_registry_handler(name, namespace, body, spec, param, **kwargs):
    if param == 'update':
        logging.info(f"Received update event for schema {name} in namespace {namespace}")
        return ops.update_schema(name=name, namespace=namespace, body=body, spec=spec, kwargs=kwargs)
    elif param == 'create':
        logging.info(f"Received create event for schema {name} in namespace {namespace}")
        return ops.create_schema(name=name, namespace=namespace, body=body, spec=spec)


def resource_is_deleted(event, **_):
    return event['type'] == 'DELETED'


@kopf.on.event("kafka.careem.com", "v1beta1", "schemaregistryschemas", when=resource_is_deleted)
async def delete_schema(namespace, body, spec, **kwargs):
    return ops.delete_schema(namespace, body, spec)


# ============================================================================
# KafkaConnect Handlers (New functionality)
# ============================================================================

# KafkaConnectEndpoint Handlers
@kopf.on.create("kafka.careem.com", "v1beta1", "kafkaconnectendpoints", timeout=60, retries=3)
async def create_endpoint(name: str, namespace: str, body, spec, **kwargs):
    """Handle creation of KafkaConnectEndpoint resources"""
    logging.info(f"Creating KafkaConnectEndpoint {name} in namespace {namespace}")
    
    try:
        result = await kc_ops.create_endpoint(name=name, namespace=namespace, body=body, spec=spec)
        logging.info(f"Successfully created endpoint {name}")
        return result
    except Exception as e:
        logging.error(f"Failed to create endpoint {name}: {str(e)}")
        raise kopf.PermanentError(f"Failed to create endpoint: {str(e)}")


@kopf.on.update("kafka.careem.com", "v1beta1", "kafkaconnectendpoints", timeout=60, retries=3)
async def update_endpoint(name: str, namespace: str, body, spec, **kwargs):
    """Handle updates to KafkaConnectEndpoint resources"""
    logging.info(f"Updating KafkaConnectEndpoint {name} in namespace {namespace}")
    
    try:
        result = await kc_ops.update_endpoint(name=name, namespace=namespace, body=body, spec=spec)
        logging.info(f"Successfully updated endpoint {name}")
        return result
    except Exception as e:
        logging.error(f"Failed to update endpoint {name}: {str(e)}")
        raise kopf.TemporaryError(f"Failed to update endpoint: {str(e)}", delay=30)


@kopf.on.delete("kafka.careem.com", "v1beta1", "kafkaconnectendpoints", timeout=60)
async def delete_endpoint(name: str, namespace: str, body, spec, **kwargs):
    """Handle deletion of KafkaConnectEndpoint resources"""
    logging.info(f"Deleting KafkaConnectEndpoint {name} in namespace {namespace}")
    
    try:
        result = await kc_ops.delete_endpoint(name=name, namespace=namespace, body=body, spec=spec)
        logging.info(f"Successfully deleted endpoint {name}")
        return result
    except Exception as e:
        logging.error(f"Failed to delete endpoint {name}: {str(e)}")
        # Don't raise error on delete to avoid blocking finalizer


# KafkaConnectPipeline Handlers
@kopf.on.create("kafka.careem.com", "v1beta1", "kafkaconnectpipelines", timeout=120, retries=3)
async def create_pipeline(name: str, namespace: str, body, spec, **kwargs):
    """Handle creation of KafkaConnectPipeline resources"""
    logging.info(f"Creating KafkaConnectPipeline {name} in namespace {namespace}")
    
    try:
        result = await kc_ops.create_pipeline(name=name, namespace=namespace, body=body, spec=spec)
        logging.info(f"Successfully created pipeline {name}")
        return result
    except Exception as e:
        logging.error(f"Failed to create pipeline {name}: {str(e)}")
        raise kopf.PermanentError(f"Failed to create pipeline: {str(e)}")


@kopf.on.update("kafka.careem.com", "v1beta1", "kafkaconnectpipelines", timeout=120, retries=3)
async def update_pipeline(name: str, namespace: str, body, spec, **kwargs):
    """Handle updates to KafkaConnectPipeline resources"""
    logging.info(f"Updating KafkaConnectPipeline {name} in namespace {namespace}")
    
    try:
        result = await kc_ops.update_pipeline(name=name, namespace=namespace, body=body, spec=spec)
        logging.info(f"Successfully updated pipeline {name}")
        return result
    except Exception as e:
        logging.error(f"Failed to update pipeline {name}: {str(e)}")
        raise kopf.TemporaryError(f"Failed to update pipeline: {str(e)}", delay=60)


@kopf.on.delete("kafka.careem.com", "v1beta1", "kafkaconnectpipelines", timeout=120)
async def delete_pipeline(name: str, namespace: str, body, spec, **kwargs):
    """Handle deletion of KafkaConnectPipeline resources"""
    logging.info(f"Deleting KafkaConnectPipeline {name} in namespace {namespace}")
    
    try:
        result = await kc_ops.delete_pipeline(name=name, namespace=namespace, body=body, spec=spec)
        logging.info(f"Successfully deleted pipeline {name}")
        return result
    except Exception as e:
        logging.error(f"Failed to delete pipeline {name}: {str(e)}")
        # Don't raise error on delete to avoid blocking finalizer


# KafkaConnectTransform Handlers
@kopf.on.create("kafka.careem.com", "v1beta1", "kafkaconnecttransforms", timeout=30, retries=3)
async def create_transform(name: str, namespace: str, body, spec, **kwargs):
    """Handle creation of KafkaConnectTransform resources"""
    logging.info(f"Creating KafkaConnectTransform {name} in namespace {namespace}")
    
    try:
        result = await kc_ops.create_transform(name=name, namespace=namespace, body=body, spec=spec)
        logging.info(f"Successfully created transform {name}")
        return result
    except Exception as e:
        logging.error(f"Failed to create transform {name}: {str(e)}")
        raise kopf.PermanentError(f"Failed to create transform: {str(e)}")


@kopf.on.update("kafka.careem.com", "v1beta1", "kafkaconnecttransforms", timeout=30, retries=3)
async def update_transform(name: str, namespace: str, body, spec, **kwargs):
    """Handle updates to KafkaConnectTransform resources"""
    logging.info(f"Updating KafkaConnectTransform {name} in namespace {namespace}")
    
    try:
        result = await kc_ops.update_transform(name=name, namespace=namespace, body=body, spec=spec)
        logging.info(f"Successfully updated transform {name}")
        return result
    except Exception as e:
        logging.error(f"Failed to update transform {name}: {str(e)}")
        raise kopf.TemporaryError(f"Failed to update transform: {str(e)}", delay=30)


@kopf.on.delete("kafka.careem.com", "v1beta1", "kafkaconnecttransforms", timeout=30)
async def delete_transform(name: str, namespace: str, body, spec, **kwargs):
    """Handle deletion of KafkaConnectTransform resources"""
    logging.info(f"Deleting KafkaConnectTransform {name} in namespace {namespace}")
    
    try:
        result = await kc_ops.delete_transform(name=name, namespace=namespace, body=body, spec=spec)
        logging.info(f"Successfully deleted transform {name}")
        return result
    except Exception as e:
        logging.error(f"Failed to delete transform {name}: {str(e)}")
        # Don't raise error on delete to avoid blocking finalizer


# Health Check Timer for Endpoints
@kopf.timer("kafka.careem.com", "v1beta1", "kafkaconnectendpoints", interval=60.0)
async def health_check_endpoints(name: str, namespace: str, body, spec, **kwargs):
    """Periodic health check for KafkaConnectEndpoint resources"""
    
    # Only run health checks if enabled
    health_check_config = spec.get('healthCheck', {})
    if not health_check_config.get('enabled', True):
        return
    
    try:
        await kc_ops.health_check_endpoint(name=name, namespace=namespace, body=body, spec=spec)
    except Exception as e:
        logging.error(f"Health check failed for endpoint {name}: {str(e)}")


# Status monitoring timer for pipelines
@kopf.timer("kafka.careem.com", "v1beta1", "kafkaconnectpipelines", interval=30.0)
async def monitor_pipeline_status(name: str, namespace: str, body, spec, **kwargs):
    """Monitor and update pipeline status"""
    
    try:
        await kc_ops.monitor_pipeline_status(name=name, namespace=namespace, body=body, spec=spec)
    except Exception as e:
        logging.error(f"Status monitoring failed for pipeline {name}: {str(e)}")


# Resume handlers for operator restart
@kopf.on.resume("kafka.careem.com", "v1beta1", "kafkaconnectpipelines")
async def resume_pipeline(name: str, namespace: str, body, spec, **kwargs):
    """Resume pipeline monitoring after operator restart"""
    logging.info(f"Resuming pipeline {name} in namespace {namespace}")
    
    try:
        await kc_ops.resume_pipeline(name=name, namespace=namespace, body=body, spec=spec)
    except Exception as e:
        logging.error(f"Failed to resume pipeline {name}: {str(e)}")


@kopf.on.resume("kafka.careem.com", "v1beta1", "kafkaconnectendpoints")
async def resume_endpoint(name: str, namespace: str, body, spec, **kwargs):
    """Resume endpoint monitoring after operator restart"""
    logging.info(f"Resuming endpoint {name} in namespace {namespace}")
    
    try:
        await kc_ops.resume_endpoint(name=name, namespace=namespace, body=body, spec=spec)
    except Exception as e:
        logging.error(f"Failed to resume endpoint {name}: {str(e)}")


# ============================================================================
# MySQLToS3 Handlers (Use-Case Specific CRDs)
# ============================================================================

# Import use-case specific operations
from mysql_to_s3_operations import MySQLToS3Operations
mysql_s3_ops = MySQLToS3Operations()

# MySQLToS3 Handlers
@kopf.on.create("kafka.careem.com", "v1beta1", "mysqltoss3", timeout=180, retries=3)
async def create_mysql_to_s3(name: str, namespace: str, body, spec, **kwargs):
    """Handle creation of MySQLToS3 resources"""
    logging.info(f"Creating MySQLToS3 pipeline {name} in namespace {namespace}")
    
    try:
        result = await mysql_s3_ops.create_mysql_to_s3(name=name, namespace=namespace, body=body, spec=spec)
        logging.info(f"Successfully created MySQLToS3 pipeline {name}")
        return result
    except Exception as e:
        logging.error(f"Failed to create MySQLToS3 pipeline {name}: {str(e)}")
        raise kopf.PermanentError(f"Failed to create MySQLToS3 pipeline: {str(e)}")


@kopf.on.update("kafka.careem.com", "v1beta1", "mysqltoss3", timeout=180, retries=3)
async def update_mysql_to_s3(name: str, namespace: str, body, spec, **kwargs):
    """Handle updates to MySQLToS3 resources"""
    logging.info(f"Updating MySQLToS3 pipeline {name} in namespace {namespace}")
    
    try:
        result = await mysql_s3_ops.update_mysql_to_s3(name=name, namespace=namespace, body=body, spec=spec)
        logging.info(f"Successfully updated MySQLToS3 pipeline {name}")
        return result
    except Exception as e:
        logging.error(f"Failed to update MySQLToS3 pipeline {name}: {str(e)}")
        raise kopf.TemporaryError(f"Failed to update MySQLToS3 pipeline: {str(e)}", delay=60)


@kopf.on.delete("kafka.careem.com", "v1beta1", "mysqltoss3", timeout=180)
async def delete_mysql_to_s3(name: str, namespace: str, body, spec, **kwargs):
    """Handle deletion of MySQLToS3 resources"""
    logging.info(f"Deleting MySQLToS3 pipeline {name} in namespace {namespace}")
    
    try:
        result = await mysql_s3_ops.delete_mysql_to_s3(name=name, namespace=namespace, body=body, spec=spec)
        logging.info(f"Successfully deleted MySQLToS3 pipeline {name}")
        return result
    except Exception as e:
        logging.error(f"Failed to delete MySQLToS3 pipeline {name}: {str(e)}")
        # Don't raise error on delete to avoid blocking finalizer


# Status monitoring timer for MySQLToS3 pipelines
@kopf.timer("kafka.careem.com", "v1beta1", "mysqltoss3", interval=30.0)
async def monitor_mysql_to_s3_status(name: str, namespace: str, body, spec, **kwargs):
    """Monitor and update MySQLToS3 pipeline status"""
    
    try:
        await mysql_s3_ops.monitor_mysql_to_s3_status(name=name, namespace=namespace, body=body, spec=spec)
    except Exception as e:
        logging.error(f"Status monitoring failed for MySQLToS3 pipeline {name}: {str(e)}")


# Resume handler for MySQLToS3 pipelines
@kopf.on.resume("kafka.careem.com", "v1beta1", "mysqltoss3")
async def resume_mysql_to_s3(name: str, namespace: str, body, spec, **kwargs):
    """Resume MySQLToS3 pipeline monitoring after operator restart"""
    logging.info(f"Resuming MySQLToS3 pipeline {name} in namespace {namespace}")
    
    try:
        await mysql_s3_ops.monitor_mysql_to_s3_status(name=name, namespace=namespace, body=body, spec=spec)
    except Exception as e:
        logging.error(f"Failed to resume MySQLToS3 pipeline {name}: {str(e)}")
