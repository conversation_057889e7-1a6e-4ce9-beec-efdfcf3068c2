# kafka-connect-operator
KafkaConnect custom Careem operator used for integrating KafkaConnect resources with Careem ecosystem and provides designated CRDs for Careem



```shell
helm repo add andrcuns https://andrcuns.github.io/charts
helm repo update


helm search repo andrcuns

helm install buildkit andrcuns/buildkit-service --namespace kafka

# apply the comments

kubectl create secret generic buildkit-regcred \
  --namespace kafka \
  --from-file=config.json=$HOME/.docker/config.json
  
kubectl patch deployment buildkit-buildkit-service \
  -n kafka \
  --type strategic \
  --patch-file ./docker/buildkit-patch.yaml


# Cleanup

helm uninstall buildkit --namespace kafka
kubectl delete secret buildkit-regcred --namespace kafka-testing
helm repo remove andrcuns
```